# DualHotbar Plugin

A Minecraft Bukkit/Spigot plugin that provides players with dual hotbars they can easily switch between using a switcher item.

## Features

- **Dual Hotbars**: Players have access to two separate hotbars (primary and secondary)
- **Easy Switching**: Switch between hotbars by selecting the switcher item slot
- **Robust Data Storage**: Multiple serialization methods with failsafe backup systems
- **Persistent Data**: All item data including NBT and persistent data containers are preserved
- **ItemsAdder Support**: Full support for custom items from ItemsAdder
- **Auto-Save**: Configurable automatic saving of player data
- **Commands**: Administrative and player commands for management
- **Permissions**: Granular permission system

## How It Works

1. **Switcher Item**: A special item (default: Nether Star) is placed in slot 9 (configurable)
2. **Switching**: When a player switches to the switcher slot, their hotbar automatically switches
3. **No Holding**: Players cannot hold or interact with the switcher item - it immediately switches hotbars
4. **Data Persistence**: All hotbar contents are saved with full item data preservation
5. **Failsafe Recovery**: Multiple backup systems ensure data is never lost

## Installation

1. Download the plugin JAR file
2. Place it in your server's `plugins` folder
3. Restart your server
4. Configure the plugin in `plugins/DualHotbar/config.yml`

## Configuration

```yaml
# The slot (in the hotbar) that will switch the hotbar to the second hotbar
# Range: 0-8
switcher-slot: 8

switcher-item:
  # Material for the switcher item
  # Supports vanilla materials and ItemsAdder items
  material: NETHER_STAR
  name: '&cSwitch to Second Hotbar'
  lore:
    - '&7Switch to the switcher slot to change hotbars'
    - '&7You cannot hold or interact with this item'

# Data storage settings
data-storage:
  # How often to auto-save player data (in minutes)
  # Set to 0 to disable auto-save
  auto-save-interval: 5
  
  # Whether to create backup files for player data
  create-backups: true
  
  # Maximum number of backup files to keep per player
  max-backups: 3

# Performance settings
performance:
  # Delay in ticks before initializing players on join
  player-init-delay: 5
  
  # Delay in ticks for hotbar updates
  hotbar-update-delay: 1
```

## Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/dualhotbar status` | Show your current hotbar status | `dualhotbar.use` |
| `/dualhotbar switch` | Manually switch between hotbars | `dualhotbar.use` |
| `/dualhotbar info` | Show plugin information | `dualhotbar.use` |
| `/dualhotbar reset` | Reset your hotbar data | `dualhotbar.reset` |
| `/dualhotbar reload` | Reload plugin configuration | `dualhotbar.reload` |
| `/dualhotbar help` | Show help message | `dualhotbar.use` |

**Aliases**: `/dh`, `/hotbar`

## Permissions

| Permission | Description | Default |
|------------|-------------|---------|
| `dualhotbar.use` | Use basic plugin commands | `true` |
| `dualhotbar.reset` | Reset hotbar data | `op` |
| `dualhotbar.reload` | Reload plugin configuration | `op` |

## ItemsAdder Support

The plugin fully supports ItemsAdder custom items. To use an ItemsAdder item as the switcher item:

```yaml
switcher-item:
  material: "itemsadder:namespace:item_name"
  # or
  material: "ia:namespace:item_name"
```

## Data Storage

Player data is stored in `plugins/DualHotbar/playerdata/` with the following features:

- **Multiple Serialization Methods**: Base64 serialization with YAML fallback
- **Automatic Backups**: Configurable backup system
- **Failsafe Recovery**: Multiple recovery mechanisms
- **Persistent Data Preservation**: All NBT and plugin data is maintained

## Technical Details

### Architecture

- **ItemSerializer**: Handles robust item serialization/deserialization
- **PlayerHotbarData**: Manages individual player hotbar states
- **PlayerDataManager**: Handles data persistence and caching
- **HotbarManager**: Core hotbar switching logic
- **PlayerInteractionListener**: Event handling for all player interactions

### Performance

- **Efficient Caching**: Player data is cached in memory for fast access
- **Asynchronous Saving**: Auto-save runs asynchronously to prevent lag
- **Minimal Event Handling**: Only necessary events are processed
- **Configurable Delays**: All timing can be adjusted for optimal performance

## Troubleshooting

### Common Issues

1. **Switcher item disappears**: Check if another plugin is interfering
2. **Items not saving**: Verify write permissions to the plugin data folder
3. **Lag on switch**: Increase `hotbar-update-delay` in config
4. **Data corruption**: The plugin has multiple recovery mechanisms built-in

### Debug Commands

Use `/dualhotbar status` to check your current hotbar state and `/dualhotbar info` for plugin information.

## Building from Source

1. Clone the repository
2. Run `./gradlew build`
3. Find the JAR in `build/libs/`

## Requirements

- **Minecraft**: 1.20+
- **Server**: Bukkit/Spigot/Paper
- **Java**: 17+

## License

This project is licensed under the MIT License.

## Support

For support, please create an issue on the GitHub repository or contact the author.

---

**Author**: xef5000  
**Version**: 1.0  
**API Version**: 1.20
