package ca.xef5000.dualHotbar.gui;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.data.PlayerHotbarData;
import ca.xef5000.dualHotbar.manager.ConfigManager;
import ca.xef5000.dualHotbar.manager.PlayerDataManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages the hotbar management GUI for players.
 */
public class HotbarGUI {
    
    private final DualHotbar plugin;
    private final ConfigManager configManager;
    private final PlayerDataManager playerDataManager;
    private final Map<UUID, Inventory> openGUIs;
    
    public HotbarGUI(DualHotbar plugin, ConfigManager configManager, PlayerDataManager playerDataManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.playerDataManager = playerDataManager;
        this.openGUIs = new HashMap<>();
    }
    
    /**
     * Opens the hotbar management GUI for a player.
     * 
     * @param player The player to open the GUI for
     */
    public void openGUI(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        
        // Create inventory
        String title = configManager.getGuiTitle();
        int size = configManager.getGuiSize();
        Inventory gui = Bukkit.createInventory(null, size, title);
        
        // Populate GUI
        populateGUI(gui, data);
        
        // Store and open
        openGUIs.put(player.getUniqueId(), gui);
        player.openInventory(gui);
    }
    
    /**
     * Populates the GUI with hotbar items and control slots.
     * 
     * @param gui The inventory to populate
     * @param data The player's hotbar data
     */
    private void populateGUI(Inventory gui, PlayerHotbarData data) {
        int switcherSlot = configManager.getSwitcherSlot();
        int totalExtraHotbars = configManager.getTotalExtraHotbars();
        
        // Clear GUI first
        gui.clear();
        
        // Populate each hotbar row
        for (int hotbarId = 1; hotbarId <= totalExtraHotbars; hotbarId++) {
            int rowStart = (hotbarId - 1) * 9;
            
            // Skip if row would exceed GUI size
            if (rowStart + 8 >= gui.getSize()) {
                break;
            }
            
            // Get hotbar items
            ItemStack[] hotbarItems = data.getHotbar(hotbarId);
            
            // Place hotbar items
            for (int slot = 0; slot < 9; slot++) {
                int guiSlot = rowStart + slot;
                
                if (slot == switcherSlot) {
                    // This is the control slot
                    ItemStack controlItem = getControlItem(data, hotbarId);
                    gui.setItem(guiSlot, controlItem);
                } else {
                    // Regular hotbar slot
                    ItemStack item = hotbarItems[slot];
                    if (item != null) {
                        gui.setItem(guiSlot, item.clone());
                    }
                }
            }
        }
        
        // Fill remaining slots with placeholder items if needed
        fillEmptySlots(gui);
    }
    
    /**
     * Gets the appropriate control item for a hotbar.
     * 
     * @param data The player's hotbar data
     * @param hotbarId The hotbar ID
     * @return The control item to display
     */
    private ItemStack getControlItem(PlayerHotbarData data, int hotbarId) {
        if (data.getCurrentHotbarId() == hotbarId) {
            // Currently equipped
            return configManager.getEquippedControlItem();
        } else if (data.isHotbarActive(hotbarId)) {
            // Active but not equipped
            return configManager.getActiveControlItem();
        } else {
            // Inactive
            return configManager.getInactiveControlItem();
        }
    }
    
    /**
     * Fills empty slots with placeholder items.
     * 
     * @param gui The inventory to fill
     */
    private void fillEmptySlots(Inventory gui) {
        ItemStack placeholder = configManager.getPlaceholderItem();
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, placeholder);
            }
        }
    }
    
    /**
     * Refreshes the GUI for a player.
     * 
     * @param player The player whose GUI to refresh
     */
    public void refreshGUI(Player player) {
        Inventory gui = openGUIs.get(player.getUniqueId());
        if (gui != null) {
            PlayerHotbarData data = playerDataManager.getPlayerData(player);
            populateGUI(gui, data);
        }
    }
    
    /**
     * Checks if a player has the GUI open.
     * 
     * @param player The player to check
     * @return true if the player has the GUI open
     */
    public boolean hasGUIOpen(Player player) {
        return openGUIs.containsKey(player.getUniqueId());
    }
    
    /**
     * Gets the GUI inventory for a player.
     * 
     * @param player The player
     * @return The GUI inventory, or null if not open
     */
    public Inventory getGUI(Player player) {
        return openGUIs.get(player.getUniqueId());
    }
    
    /**
     * Closes the GUI for a player.
     * 
     * @param player The player whose GUI to close
     */
    public void closeGUI(Player player) {
        openGUIs.remove(player.getUniqueId());
    }
    
    /**
     * Handles a click in the GUI.
     * 
     * @param player The player who clicked
     * @param slot The slot that was clicked
     * @param clickedItem The item that was clicked
     * @return true if the click was handled, false otherwise
     */
    public boolean handleClick(Player player, int slot, ItemStack clickedItem) {
        if (!hasGUIOpen(player)) {
            return false;
        }
        
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        int switcherSlot = configManager.getSwitcherSlot();
        
        // Calculate which hotbar and slot this corresponds to
        int row = slot / 9;
        int slotInRow = slot % 9;
        int hotbarId = row + 1; // GUI rows start from hotbar 1
        
        // Check if this is a valid hotbar
        if (hotbarId > configManager.getTotalExtraHotbars()) {
            return true; // Cancel click but don't process
        }
        
        if (slotInRow == switcherSlot) {
            // This is a control slot click
            return handleControlSlotClick(player, hotbarId, data);
        } else {
            // This is a regular slot click
            return handleRegularSlotClick(player, hotbarId, slotInRow, data);
        }
    }
    
    /**
     * Handles a click on a control slot.
     * 
     * @param player The player who clicked
     * @param hotbarId The hotbar ID
     * @param data The player's hotbar data
     * @return true to cancel the click
     */
    private boolean handleControlSlotClick(Player player, int hotbarId, PlayerHotbarData data) {
        if (data.getCurrentHotbarId() == hotbarId) {
            // Can't deactivate currently equipped hotbar
            player.sendMessage("§cCannot deactivate the currently equipped hotbar!");
            return true;
        }
        
        if (data.isHotbarActive(hotbarId)) {
            // Deactivate hotbar
            if (data.deactivateHotbar(hotbarId)) {
                player.sendMessage("§aHotbar " + hotbarId + " deactivated!");
                refreshGUI(player);
            } else {
                player.sendMessage("§cCannot deactivate this hotbar!");
            }
        } else {
            // Activate hotbar
            if (data.activateHotbar(hotbarId, configManager.getMaxActiveHotbars())) {
                player.sendMessage("§aHotbar " + hotbarId + " activated!");
                refreshGUI(player);
            } else {
                player.sendMessage("§cMaximum number of active hotbars reached! Deactivate another hotbar first.");
            }
        }
        
        return true; // Cancel the click
    }
    
    /**
     * Handles a click on a regular slot.
     * 
     * @param player The player who clicked
     * @param hotbarId The hotbar ID
     * @param slotInRow The slot within the row
     * @param data The player's hotbar data
     * @return true to cancel the click
     */
    private boolean handleRegularSlotClick(Player player, int hotbarId, int slotInRow, PlayerHotbarData data) {
        // Check if this hotbar is currently equipped
        if (data.getCurrentHotbarId() == hotbarId) {
            player.sendMessage("§cCannot edit the currently equipped hotbar in the GUI!");
            return true;
        }
        
        // Allow editing of non-equipped hotbars
        return false; // Don't cancel - allow normal inventory interaction
    }
    
    /**
     * Saves changes from the GUI back to the player's hotbar data.
     * 
     * @param player The player whose data to save
     */
    public void saveGUIChanges(Player player) {
        Inventory gui = openGUIs.get(player.getUniqueId());
        if (gui == null) {
            return;
        }
        
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        int switcherSlot = configManager.getSwitcherSlot();
        int totalExtraHotbars = configManager.getTotalExtraHotbars();
        
        // Save each hotbar from the GUI
        for (int hotbarId = 1; hotbarId <= totalExtraHotbars; hotbarId++) {
            int rowStart = (hotbarId - 1) * 9;
            
            // Skip if row would exceed GUI size
            if (rowStart + 8 >= gui.getSize()) {
                break;
            }
            
            // Extract hotbar items from GUI
            ItemStack[] hotbarItems = new ItemStack[9];
            for (int slot = 0; slot < 9; slot++) {
                if (slot != switcherSlot) { // Skip control slot
                    int guiSlot = rowStart + slot;
                    ItemStack item = gui.getItem(guiSlot);
                    
                    // Don't save placeholder items
                    if (item != null && !isPlaceholderItem(item)) {
                        hotbarItems[slot] = item.clone();
                    }
                }
            }
            
            // Save to player data
            data.setHotbar(hotbarId, hotbarItems);
        }
    }
    
    /**
     * Checks if an item is a placeholder item.
     * 
     * @param item The item to check
     * @return true if it's a placeholder item
     */
    private boolean isPlaceholderItem(ItemStack item) {
        if (item == null) {
            return false;
        }
        
        ItemStack placeholder = configManager.getPlaceholderItem();
        return item.isSimilar(placeholder);
    }
}
