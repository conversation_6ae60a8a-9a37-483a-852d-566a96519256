package ca.xef5000.dualHotbar.data;

import ca.xef5000.dualHotbar.util.ItemSerializer;
import org.bukkit.inventory.ItemStack;

import java.util.Arrays;
import java.util.UUID;

/**
 * Represents the hotbar data for a player, including both primary and secondary hotbars.
 * This class handles the storage and management of player hotbar states.
 */
public class PlayerHotbarData {
    
    private final UUID playerId;
    private ItemStack[] primaryHotbar;
    private ItemStack[] secondaryHotbar;
    private boolean isUsingSecondaryHotbar;
    private int currentHeldItemSlot;
    private long lastSwitchTime;
    
    // Backup data for failsafe recovery
    private String primaryHotbarBackup;
    private String secondaryHotbarBackup;
    
    /**
     * Creates a new PlayerHotbarData instance for the specified player.
     * 
     * @param playerId The UUID of the player
     */
    public PlayerHotbarData(UUID playerId) {
        this.playerId = playerId;
        this.primaryHotbar = new ItemStack[9];
        this.secondaryHotbar = new ItemStack[9];
        this.isUsingSecondaryHotbar = false;
        this.currentHeldItemSlot = 0;
        this.lastSwitchTime = 0;
        
        // Initialize backup strings
        this.primaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.primaryHotbar);
        this.secondaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.secondaryHotbar);
    }
    
    /**
     * Gets the player's UUID.
     * 
     * @return The player's UUID
     */
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Gets the primary hotbar items.
     * 
     * @return Array of ItemStacks representing the primary hotbar
     */
    public ItemStack[] getPrimaryHotbar() {
        return primaryHotbar != null ? Arrays.copyOf(primaryHotbar, primaryHotbar.length) : new ItemStack[9];
    }
    
    /**
     * Sets the primary hotbar items.
     * 
     * @param items Array of ItemStacks to set as the primary hotbar
     */
    public void setPrimaryHotbar(ItemStack[] items) {
        if (items != null && items.length == 9) {
            this.primaryHotbar = Arrays.copyOf(items, items.length);
            // Create backup
            this.primaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.primaryHotbar);
        }
    }
    
    /**
     * Gets the secondary hotbar items.
     * 
     * @return Array of ItemStacks representing the secondary hotbar
     */
    public ItemStack[] getSecondaryHotbar() {
        return secondaryHotbar != null ? Arrays.copyOf(secondaryHotbar, secondaryHotbar.length) : new ItemStack[9];
    }
    
    /**
     * Sets the secondary hotbar items.
     * 
     * @param items Array of ItemStacks to set as the secondary hotbar
     */
    public void setSecondaryHotbar(ItemStack[] items) {
        if (items != null && items.length == 9) {
            this.secondaryHotbar = Arrays.copyOf(items, items.length);
            // Create backup
            this.secondaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.secondaryHotbar);
        }
    }
    
    /**
     * Gets the currently active hotbar based on the current state.
     * 
     * @return Array of ItemStacks representing the currently active hotbar
     */
    public ItemStack[] getCurrentHotbar() {
        return isUsingSecondaryHotbar ? getSecondaryHotbar() : getPrimaryHotbar();
    }
    
    /**
     * Gets the currently inactive hotbar based on the current state.
     * 
     * @return Array of ItemStacks representing the currently inactive hotbar
     */
    public ItemStack[] getInactiveHotbar() {
        return isUsingSecondaryHotbar ? getPrimaryHotbar() : getSecondaryHotbar();
    }
    
    /**
     * Checks if the player is currently using the secondary hotbar.
     * 
     * @return true if using secondary hotbar, false if using primary
     */
    public boolean isUsingSecondaryHotbar() {
        return isUsingSecondaryHotbar;
    }
    
    /**
     * Sets whether the player is using the secondary hotbar.
     * 
     * @param usingSecondary true to use secondary hotbar, false for primary
     */
    public void setUsingSecondaryHotbar(boolean usingSecondary) {
        this.isUsingSecondaryHotbar = usingSecondary;
        this.lastSwitchTime = System.currentTimeMillis();
    }
    
    /**
     * Switches between primary and secondary hotbars.
     * 
     * @return true if switched to secondary, false if switched to primary
     */
    public boolean switchHotbar() {
        this.isUsingSecondaryHotbar = !this.isUsingSecondaryHotbar;
        this.lastSwitchTime = System.currentTimeMillis();
        return this.isUsingSecondaryHotbar;
    }
    
    /**
     * Gets the current held item slot.
     * 
     * @return The slot number (0-8) of the currently held item
     */
    public int getCurrentHeldItemSlot() {
        return currentHeldItemSlot;
    }
    
    /**
     * Sets the current held item slot.
     * 
     * @param slot The slot number (0-8) to set as currently held
     */
    public void setCurrentHeldItemSlot(int slot) {
        if (slot >= 0 && slot <= 8) {
            this.currentHeldItemSlot = slot;
        }
    }
    
    /**
     * Gets the timestamp of the last hotbar switch.
     * 
     * @return The timestamp in milliseconds
     */
    public long getLastSwitchTime() {
        return lastSwitchTime;
    }
    
    /**
     * Updates the current hotbar with new items.
     * 
     * @param items The new items to set in the current hotbar
     */
    public void updateCurrentHotbar(ItemStack[] items) {
        if (isUsingSecondaryHotbar) {
            setSecondaryHotbar(items);
        } else {
            setPrimaryHotbar(items);
        }
    }
    
    /**
     * Updates the inactive hotbar with new items.
     * 
     * @param items The new items to set in the inactive hotbar
     */
    public void updateInactiveHotbar(ItemStack[] items) {
        if (isUsingSecondaryHotbar) {
            setPrimaryHotbar(items);
        } else {
            setSecondaryHotbar(items);
        }
    }
    
    /**
     * Attempts to recover data from backups in case of corruption.
     * 
     * @return true if recovery was successful, false otherwise
     */
    public boolean recoverFromBackup() {
        boolean recovered = false;
        
        // Try to recover primary hotbar
        if (primaryHotbarBackup != null) {
            ItemStack[] recoveredPrimary = ItemSerializer.deserializeItemStackArray(primaryHotbarBackup);
            if (recoveredPrimary != null) {
                this.primaryHotbar = recoveredPrimary;
                recovered = true;
            }
        }
        
        // Try to recover secondary hotbar
        if (secondaryHotbarBackup != null) {
            ItemStack[] recoveredSecondary = ItemSerializer.deserializeItemStackArray(secondaryHotbarBackup);
            if (recoveredSecondary != null) {
                this.secondaryHotbar = recoveredSecondary;
                recovered = true;
            }
        }
        
        return recovered;
    }
    
    /**
     * Creates a backup of the current hotbar states.
     */
    public void createBackup() {
        this.primaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.primaryHotbar);
        this.secondaryHotbarBackup = ItemSerializer.serializeItemStackArray(this.secondaryHotbar);
    }
    
    /**
     * Checks if the player has recently switched hotbars (within the last 100ms).
     * This helps prevent rapid switching issues.
     * 
     * @return true if recently switched, false otherwise
     */
    public boolean hasRecentlySwitched() {
        return System.currentTimeMillis() - lastSwitchTime < 100;
    }
    
    /**
     * Resets the player's hotbar data to default state.
     */
    public void reset() {
        this.primaryHotbar = new ItemStack[9];
        this.secondaryHotbar = new ItemStack[9];
        this.isUsingSecondaryHotbar = false;
        this.currentHeldItemSlot = 0;
        this.lastSwitchTime = 0;
        createBackup();
    }
}
