package ca.xef5000.dualHotbar.listener;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.gui.HotbarGUI;
import ca.xef5000.dualHotbar.manager.ConfigManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;

/**
 * Handles events related to the hotbar management GUI.
 */
public class HotbarGUIListener implements Listener {
    
    private final DualHotbar plugin;
    private final HotbarGUI hotbarGUI;
    private final ConfigManager configManager;
    
    public HotbarGUIListener(DualHotbar plugin, HotbarGUI hotbarGUI, ConfigManager configManager) {
        this.plugin = plugin;
        this.hotbarGUI = hotbarGUI;
        this.configManager = configManager;
    }
    
    /**
     * Handles inventory click events in the hotbar GUI.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if this is our GUI
        if (!hotbarGUI.hasGUIOpen(player)) {
            return;
        }
        
        Inventory clickedInventory = event.getClickedInventory();
        Inventory playerGUI = hotbarGUI.getGUI(player);
        
        // If clicking in the GUI
        if (clickedInventory != null && clickedInventory.equals(playerGUI)) {
            // Handle the click
            boolean shouldCancel = hotbarGUI.handleClick(player, event.getSlot(), event.getCurrentItem());
            
            if (shouldCancel) {
                event.setCancelled(true);
            }
        }
        // If clicking in player inventory while GUI is open
        else if (clickedInventory != null && clickedInventory.equals(player.getInventory())) {
            // Prevent moving items from player inventory to GUI in certain cases
            if (event.getClick().isShiftClick()) {
                // Cancel shift-click to prevent items going to GUI
                event.setCancelled(true);
            }
        }
    }
    
    /**
     * Handles inventory drag events in the hotbar GUI.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if this is our GUI
        if (!hotbarGUI.hasGUIOpen(player)) {
            return;
        }
        
        Inventory playerGUI = hotbarGUI.getGUI(player);
        
        // Check if any of the dragged slots are in our GUI
        for (int slot : event.getRawSlots()) {
            if (slot < playerGUI.getSize()) {
                // Dragging in our GUI - need to check if it's allowed
                int row = slot / 9;
                int slotInRow = slot % 9;
                int hotbarId = row + 1;
                int switcherSlot = configManager.getSwitcherSlot();
                
                // Cancel if dragging to control slot or invalid hotbar
                if (slotInRow == switcherSlot || hotbarId > configManager.getTotalExtraHotbars()) {
                    event.setCancelled(true);
                    return;
                }
                
                // Cancel if dragging to currently equipped hotbar
                if (hotbarGUI.hasGUIOpen(player)) {
                    // We'll implement this check in the GUI class
                    event.setCancelled(true);
                    player.sendMessage("§cUse individual clicks to edit hotbar items!");
                    return;
                }
            }
        }
    }
    
    /**
     * Handles inventory close events to save changes.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        
        // Check if this is our GUI
        if (!hotbarGUI.hasGUIOpen(player)) {
            return;
        }
        
        // Save any changes made in the GUI
        hotbarGUI.saveGUIChanges(player);
        
        // Remove from tracking
        hotbarGUI.closeGUI(player);
        
        plugin.getLogger().info("Saved GUI changes for player: " + player.getName());
    }
}
