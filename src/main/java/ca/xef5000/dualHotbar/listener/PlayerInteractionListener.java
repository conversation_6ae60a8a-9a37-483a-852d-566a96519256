package ca.xef5000.dualHotbar.listener;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.manager.ConfigManager;
import ca.xef5000.dualHotbar.manager.HotbarManager;
import ca.xef5000.dualHotbar.manager.PlayerDataManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.player.*;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * Handles all player interaction events related to the dual hotbar functionality.
 */
public class PlayerInteractionListener implements Listener {
    
    private final DualHotbar plugin;
    private final HotbarManager hotbarManager;
    private final PlayerDataManager playerDataManager;
    private final ConfigManager configManager;
    
    /**
     * Creates a new PlayerInteractionListener.
     * 
     * @param plugin The DualHotbar plugin instance
     * @param hotbarManager The hotbar manager
     * @param playerDataManager The player data manager
     * @param configManager The configuration manager
     */
    public PlayerInteractionListener(DualHotbar plugin, HotbarManager hotbarManager, 
                                   PlayerDataManager playerDataManager, ConfigManager configManager) {
        this.plugin = plugin;
        this.hotbarManager = hotbarManager;
        this.playerDataManager = playerDataManager;
        this.configManager = configManager;
    }
    
    /**
     * Handles player join events to initialize their hotbar.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Delay initialization to ensure player is fully loaded
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    hotbarManager.initializePlayer(player);
                }
            }
        }.runTaskLater(plugin, configManager.getPlayerInitDelay());
    }
    
    /**
     * Handles player quit events to save their hotbar data.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        hotbarManager.handlePlayerLogout(player);
    }
    
    /**
     * Handles player item held events to detect switcher slot switching.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        int newSlot = event.getNewSlot();
        int switcherSlot = configManager.getSwitcherSlot();
        
        // Check if player is switching to the switcher slot
        if (newSlot == switcherSlot) {
            // Cancel the event to prevent holding the switcher item
            event.setCancelled(true);
            
            // Handle the hotbar switch
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        hotbarManager.handleSwitcherSlotSwitch(player);
                    }
                }
            }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
        } else {
            // Update the held slot in player data
            playerDataManager.getPlayerData(player).setCurrentHeldItemSlot(newSlot);
        }
    }
    
    /**
     * Handles inventory click events to prevent interaction with the switcher item.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if the click involves the switcher item
        if (event.getCurrentItem() != null && hotbarManager.isSwitcherItem(event.getCurrentItem())) {
            // Cancel interaction with switcher item
            event.setCancelled(true);
            return;
        }
        
        // Check if clicking in hotbar slots
        if (event.getInventory().getType() == InventoryType.PLAYER && event.getSlot() < 9) {
            int switcherSlot = configManager.getSwitcherSlot();
            
            // Prevent placing items in the switcher slot
            if (event.getSlot() == switcherSlot) {
                event.setCancelled(true);
                
                // Ensure switcher item is still there
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (player.isOnline()) {
                            hotbarManager.ensureSwitcherItem(player);
                        }
                    }
                }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());

                return;
            }

            // Update hotbar data after inventory changes
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        hotbarManager.updateCurrentHotbar(player);
                    }
                }
            }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
        }
    }
    
    /**
     * Handles player interact events to prevent using the switcher item.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        if (event.getItem() != null && hotbarManager.isSwitcherItem(event.getItem())) {
            // Cancel interaction with switcher item
            event.setCancelled(true);
            
            // Ensure player is not holding the switcher item
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        int switcherSlot = configManager.getSwitcherSlot();
                        if (player.getInventory().getHeldItemSlot() == switcherSlot) {
                            hotbarManager.handleSwitcherSlotSwitch(player);
                        }
                    }
                }
            }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
        }
    }

    /**
     * Handles player drop item events to prevent dropping the switcher item.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        Player player = event.getPlayer();

        if (hotbarManager.isSwitcherItem(event.getItemDrop().getItemStack())) {
            // Cancel dropping the switcher item
            event.setCancelled(true);

            // Ensure switcher item is restored
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        hotbarManager.ensureSwitcherItem(player);
                    }
                }
            }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
        }
    }
    
    /**
     * Handles player swap hand events to prevent swapping the switcher item.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerSwapHandItems(PlayerSwapHandItemsEvent event) {
        Player player = event.getPlayer();
        
        if ((event.getMainHandItem() != null && hotbarManager.isSwitcherItem(event.getMainHandItem())) ||
            (event.getOffHandItem() != null && hotbarManager.isSwitcherItem(event.getOffHandItem()))) {
            
            // Cancel swapping with switcher item
            event.setCancelled(true);
            
            // Ensure switcher item is in correct position
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        hotbarManager.ensureSwitcherItem(player);
                    }
                }
            }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
        }
    }

    /**
     * Handles player respawn events to restore hotbar state.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();

        // Restore hotbar after respawn
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    hotbarManager.initializePlayer(player);
                }
            }
        }.runTaskLater(plugin, configManager.getPlayerInitDelay());
    }
    
    /**
     * Handles player world change events to maintain hotbar state.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        
        // Ensure hotbar state is maintained across worlds
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    hotbarManager.ensureSwitcherItem(player);
                }
            }
        }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
    }

    /**
     * Handles player game mode change events to maintain hotbar state.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerGameModeChange(PlayerGameModeChangeEvent event) {
        Player player = event.getPlayer();

        // Ensure hotbar state is maintained across game mode changes
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    hotbarManager.ensureSwitcherItem(player);
                }
            }
        }.runTaskLater(plugin, configManager.getHotbarUpdateDelay());
    }
}
