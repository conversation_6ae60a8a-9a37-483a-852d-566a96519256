package ca.xef5000.dualHotbar.util;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.io.BukkitObjectInputStream;
import org.bukkit.util.io.BukkitObjectOutputStream;

import java.io.*;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for robust ItemStack serialization and deserialization.
 * Supports multiple serialization methods with fallbacks to ensure data integrity.
 */
public class ItemSerializer {
    
    private static final Logger logger = Logger.getLogger(ItemSerializer.class.getName());
    
    /**
     * Serializes an ItemStack to a Base64 string using Bukkit's serialization.
     * This method preserves all ItemStack data including persistent data containers.
     * 
     * @param item The ItemStack to serialize
     * @return Base64 encoded string representation of the ItemStack, or null if serialization fails
     */
    public static String serializeItemStack(ItemStack item) {
        if (item == null) {
            return null;
        }
        
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);
            
            dataOutput.writeObject(item);
            dataOutput.close();
            
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to serialize ItemStack: " + item.getType(), e);
            return null;
        }
    }
    
    /**
     * Deserializes an ItemStack from a Base64 string.
     * 
     * @param data The Base64 encoded string representation of the ItemStack
     * @return The deserialized ItemStack, or null if deserialization fails
     */
    public static ItemStack deserializeItemStack(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        
        try {
            byte[] bytes = Base64.getDecoder().decode(data);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);
            
            ItemStack item = (ItemStack) dataInput.readObject();
            dataInput.close();
            
            return item;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to deserialize ItemStack from data: " + data.substring(0, Math.min(50, data.length())), e);
            return null;
        }
    }
    
    /**
     * Serializes an ItemStack to a ConfigurationSection as a fallback method.
     * This method is more compatible across different server versions.
     * 
     * @param item The ItemStack to serialize
     * @return ConfigurationSection containing the ItemStack data, or null if serialization fails
     */
    public static ConfigurationSection serializeItemStackToConfig(ItemStack item) {
        if (item == null) {
            return null;
        }
        
        try {
            Map<String, Object> serialized = item.serialize();
            ConfigurationSection section = new MemoryConfiguration();
            
            for (Map.Entry<String, Object> entry : serialized.entrySet()) {
                section.set(entry.getKey(), entry.getValue());
            }
            
            return section;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to serialize ItemStack to config: " + item.getType(), e);
            return null;
        }
    }
    
    /**
     * Deserializes an ItemStack from a ConfigurationSection.
     * 
     * @param section The ConfigurationSection containing the ItemStack data
     * @return The deserialized ItemStack, or null if deserialization fails
     */
    public static ItemStack deserializeItemStackFromConfig(ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        try {
            Map<String, Object> serialized = new HashMap<>();
            for (String key : section.getKeys(false)) {
                serialized.put(key, section.get(key));
            }
            
            return ItemStack.deserialize(serialized);
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to deserialize ItemStack from config section", e);
            return null;
        }
    }
    
    /**
     * Serializes an array of ItemStacks to a Base64 string.
     * 
     * @param items The ItemStack array to serialize
     * @return Base64 encoded string representation of the ItemStack array
     */
    public static String serializeItemStackArray(ItemStack[] items) {
        if (items == null) {
            return null;
        }
        
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);
            
            dataOutput.writeInt(items.length);
            for (ItemStack item : items) {
                dataOutput.writeObject(item);
            }
            dataOutput.close();
            
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to serialize ItemStack array", e);
            return null;
        }
    }
    
    /**
     * Deserializes an array of ItemStacks from a Base64 string.
     * 
     * @param data The Base64 encoded string representation of the ItemStack array
     * @return The deserialized ItemStack array, or null if deserialization fails
     */
    public static ItemStack[] deserializeItemStackArray(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        
        try {
            byte[] bytes = Base64.getDecoder().decode(data);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);
            
            int length = dataInput.readInt();
            ItemStack[] items = new ItemStack[length];
            
            for (int i = 0; i < length; i++) {
                items[i] = (ItemStack) dataInput.readObject();
            }
            dataInput.close();
            
            return items;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to deserialize ItemStack array from data", e);
            return null;
        }
    }
    
    /**
     * Creates a deep copy of an ItemStack.
     * 
     * @param original The ItemStack to copy
     * @return A deep copy of the ItemStack, or null if the original is null
     */
    public static ItemStack cloneItemStack(ItemStack original) {
        if (original == null) {
            return null;
        }
        
        return original.clone();
    }
    
    /**
     * Checks if two ItemStacks are similar (same material, amount, and meta).
     * 
     * @param item1 First ItemStack
     * @param item2 Second ItemStack
     * @return true if the ItemStacks are similar, false otherwise
     */
    public static boolean areItemsSimilar(ItemStack item1, ItemStack item2) {
        if (item1 == null && item2 == null) {
            return true;
        }
        if (item1 == null || item2 == null) {
            return false;
        }
        
        return item1.isSimilar(item2);
    }
}
