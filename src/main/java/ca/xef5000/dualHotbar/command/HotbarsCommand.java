package ca.xef5000.dualHotbar.command;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.gui.HotbarGUI;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Command handler for the hotbar GUI command.
 */
public class HotbarsCommand implements CommandExecutor {
    
    private final DualHotbar plugin;
    private final HotbarGUI hotbarGUI;
    
    public HotbarsCommand(DualHotbar plugin, HotbarGUI hotbarGUI) {
        this.plugin = plugin;
        this.hotbarGUI = hotbarGUI;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        hotbarGUI.openGUI(player);
        
        return true;
    }
}
