package ca.xef5000.dualHotbar.command;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.data.PlayerHotbarData;
import ca.xef5000.dualHotbar.manager.HotbarManager;
import ca.xef5000.dualHotbar.manager.PlayerDataManager;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command handler for DualHotbar plugin commands.
 */
public class DualHotbarCommand implements CommandExecutor, TabCompleter {
    
    private final DualHotbar plugin;
    private final HotbarManager hotbarManager;
    private final PlayerDataManager playerDataManager;
    
    public DualHotbarCommand(DualHotbar plugin) {
        this.plugin = plugin;
        this.hotbarManager = plugin.getHotbarManager();
        this.playerDataManager = plugin.getPlayerDataManager();
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "status":
                return handleStatus(sender, args);
            case "switch":
                return handleSwitch(sender, args);
            case "reset":
                return handleReset(sender, args);
            case "reload":
                return handleReload(sender, args);
            case "info":
                return handleInfo(sender, args);
            case "help":
                sendHelp(sender);
                return true;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand: " + subCommand);
                sendHelp(sender);
                return true;
        }
    }
    
    private boolean handleStatus(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        
        sender.sendMessage(ChatColor.GOLD + "=== DualHotbar Status ===");
        sender.sendMessage(ChatColor.YELLOW + "Current Hotbar: " + ChatColor.WHITE + 
                          (data.isUsingSecondaryHotbar() ? "Secondary" : "Primary"));
        sender.sendMessage(ChatColor.YELLOW + "Held Slot: " + ChatColor.WHITE + data.getCurrentHeldItemSlot());
        sender.sendMessage(ChatColor.YELLOW + "Last Switch: " + ChatColor.WHITE + 
                          (System.currentTimeMillis() - data.getLastSwitchTime()) + "ms ago");
        
        return true;
    }
    
    private boolean handleSwitch(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        boolean switched = hotbarManager.switchHotbar(player);
        
        if (switched) {
            String newHotbar = hotbarManager.getHotbarState(player);
            sender.sendMessage(ChatColor.GREEN + "Switched to " + newHotbar + " hotbar!");
        } else {
            sender.sendMessage(ChatColor.RED + "Could not switch hotbar. Please wait a moment and try again.");
        }
        
        return true;
    }
    
    private boolean handleReset(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return true;
        }
        
        if (!sender.hasPermission("dualhotbar.reset")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        Player player = (Player) sender;
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        data.reset();
        
        // Re-initialize the player
        hotbarManager.initializePlayer(player);
        
        sender.sendMessage(ChatColor.GREEN + "Your hotbar data has been reset!");
        return true;
    }
    
    private boolean handleReload(CommandSender sender, String[] args) {
        if (!sender.hasPermission("dualhotbar.reload")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        plugin.reloadConfig();
        plugin.getConfigManager().loadConfig();
        
        sender.sendMessage(ChatColor.GREEN + "DualHotbar configuration reloaded!");
        return true;
    }
    
    private boolean handleInfo(CommandSender sender, String[] args) {
        sender.sendMessage(ChatColor.GOLD + "=== DualHotbar Plugin Info ===");
        sender.sendMessage(ChatColor.YELLOW + "Version: " + ChatColor.WHITE + plugin.getDescription().getVersion());
        sender.sendMessage(ChatColor.YELLOW + "Author: " + ChatColor.WHITE + "xef5000");
        sender.sendMessage(ChatColor.YELLOW + "Cached Players: " + ChatColor.WHITE + playerDataManager.getCachedPlayerCount());
        sender.sendMessage(ChatColor.YELLOW + "Switcher Slot: " + ChatColor.WHITE + plugin.getConfigManager().getSwitcherSlot());
        
        return true;
    }
    
    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== DualHotbar Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/dualhotbar status" + ChatColor.WHITE + " - Show your hotbar status");
        sender.sendMessage(ChatColor.YELLOW + "/dualhotbar switch" + ChatColor.WHITE + " - Manually switch hotbars");
        sender.sendMessage(ChatColor.YELLOW + "/dualhotbar info" + ChatColor.WHITE + " - Show plugin information");
        
        if (sender.hasPermission("dualhotbar.reset")) {
            sender.sendMessage(ChatColor.YELLOW + "/dualhotbar reset" + ChatColor.WHITE + " - Reset your hotbar data");
        }
        
        if (sender.hasPermission("dualhotbar.reload")) {
            sender.sendMessage(ChatColor.YELLOW + "/dualhotbar reload" + ChatColor.WHITE + " - Reload plugin configuration");
        }
        
        sender.sendMessage(ChatColor.YELLOW + "/dualhotbar help" + ChatColor.WHITE + " - Show this help message");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("status", "switch", "info", "help");
            
            if (sender.hasPermission("dualhotbar.reset")) {
                subCommands = new ArrayList<>(subCommands);
                subCommands.add("reset");
            }
            
            if (sender.hasPermission("dualhotbar.reload")) {
                subCommands = new ArrayList<>(subCommands);
                subCommands.add("reload");
            }
            
            String input = args[0].toLowerCase();
            for (String subCommand : subCommands) {
                if (subCommand.startsWith(input)) {
                    completions.add(subCommand);
                }
            }
        }
        
        return completions;
    }
}
