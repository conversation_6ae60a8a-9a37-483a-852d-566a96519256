package ca.xef5000.dualHotbar.manager;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.data.PlayerHotbarData;
import ca.xef5000.dualHotbar.util.ItemSerializer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Arrays;
import java.util.logging.Logger;

/**
 * Core manager for handling multi-hotbar functionality.
 * Manages hotbar cycling, item placement, and switcher item behavior.
 */
public class HotbarManager {

    private final DualHotbar plugin;
    private final PlayerDataManager playerDataManager;
    private final ConfigManager configManager;
    private final Logger logger;

    /**
     * Creates a new HotbarManager instance.
     * 
     * @param plugin The DualHotbar plugin instance
     * @param playerDataManager The player data manager
     * @param configManager The configuration manager
     */
    public HotbarManager(DualHotbar plugin, PlayerDataManager playerDataManager, ConfigManager configManager) {
        this.plugin = plugin;
        this.playerDataManager = playerDataManager;
        this.configManager = configManager;
        this.logger = plugin.getLogger();

        // Log the independent slots configuration
        int[] independentSlots = configManager.getIndependentSlots();
        StringBuilder sb = new StringBuilder("Independent slots configured: ");
        for (int i = 0; i < independentSlots.length; i++) {
            sb.append(independentSlots[i]);
            if (i < independentSlots.length - 1) {
                sb.append(", ");
            }
        }
        logger.info(sb.toString());
    }

    /**
     * Initializes a player's hotbar with the switcher item.
     * Called when a player joins the server.
     * 
     * @param player The player to initialize
     */
    public void initializePlayer(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);

        // Save current hotbar as primary if it's the first time
        if (isHotbarEmpty(data.getPrimaryHotbar()) && isHotbarEmpty(data.getSecondaryHotbar())) {
            ItemStack[] currentHotbar = getPlayerHotbar(player);
            data.setPrimaryHotbar(currentHotbar);
        }

        // Ensure switcher item is in the correct slot
        ensureSwitcherItem(player);

        // Update held item slot
        data.setCurrentHeldItemSlot(player.getInventory().getHeldItemSlot());

        logger.info("Initialized player: " + player.getName());
    }

    /**
     * Ensures the switcher item is present in the configured slot.
     * 
     * @param player The player to check
     */
    public void ensureSwitcherItem(Player player) {
        PlayerInventory inventory = player.getInventory();
        int switcherSlot = configManager.getSwitcherSlot();
        ItemStack switcherItem = configManager.getSwitcherItem();

        // Check if the switcher item is already in the correct slot
        ItemStack currentItem = inventory.getItem(switcherSlot);
        if (currentItem == null || !isSwitcherItem(currentItem)) {
            inventory.setItem(switcherSlot, switcherItem);
        }
    }

    /**
     * Handles hotbar switching when a player switches to the switcher slot.
     * 
     * @param player The player switching hotbars
     * @return true if the switch was successful, false otherwise
     */
    public boolean switchHotbar(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);

        // Prevent rapid switching
        if (data.hasRecentlySwitched()) {
            return false;
        }

        // Save current hotbar state
        ItemStack[] currentHotbar = getPlayerHotbar(player);
        data.updateCurrentHotbar(currentHotbar);

        // Switch to the other hotbar
        boolean switchedToSecondary = data.switchHotbar();

        // Log which slots are independent and will be skipped
        StringBuilder sb = new StringBuilder("Skipping independent slots during hotbar swap for " + player.getName() + ": ");
        int[] independentSlots = configManager.getIndependentSlots();
        for (int i = 0; i < independentSlots.length; i++) {
            sb.append(independentSlots[i]);
            if (i < independentSlots.length - 1) {
                sb.append(", ");
            }
        }
        logger.info(sb.toString());

        // Load the new hotbar
        ItemStack[] newHotbar = data.getCurrentHotbar();
        setPlayerHotbar(player, newHotbar);

        // Ensure switcher item is in the correct slot
        ensureSwitcherItem(player);

        // Restore the player's held item slot (but not if it's the switcher slot)
        int heldSlot = data.getCurrentHeldItemSlot();
        int switcherSlot = configManager.getSwitcherSlot();

        if (heldSlot != switcherSlot) {
            // Delay the slot change to prevent issues
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        player.getInventory().setHeldItemSlot(heldSlot);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }

        logger.info("Player " + player.getName() + " switched to " + 
                   (switchedToSecondary ? "secondary" : "primary") + " hotbar");

        return true;
    }

    /**
     * Handles when a player switches to the switcher slot.
     * This method ensures the player doesn't actually hold the switcher item.
     * 
     * @param player The player who switched to the switcher slot
     */
    public void handleSwitcherSlotSwitch(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        int switcherSlot = configManager.getSwitcherSlot();

        // Update the held slot in data
        data.setCurrentHeldItemSlot(player.getInventory().getHeldItemSlot());

        // Switch hotbar
        if (switchHotbar(player)) {
            // Move player away from switcher slot immediately
            int newSlot = findNonSwitcherSlot(switcherSlot);

            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        //player.getInventory().setHeldItemSlot(newSlot);
                        data.setCurrentHeldItemSlot(newSlot);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
    }

    /**
     * Updates the current hotbar data when the player's inventory changes.
     * 
     * @param player The player whose inventory changed
     */
    public void updateCurrentHotbar(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        ItemStack[] currentHotbar = getPlayerHotbar(player);
        data.updateCurrentHotbar(currentHotbar);
    }

    /**
     * Gets the player's current hotbar items.
     * 
     * @param player The player
     * @return Array of ItemStacks representing the hotbar
     */
    private ItemStack[] getPlayerHotbar(Player player) {
        PlayerInventory inventory = player.getInventory();
        ItemStack[] hotbar = new ItemStack[9];

        for (int i = 0; i < 9; i++) {
            // Skip independent slots - they won't be saved or swapped
            if (configManager.isIndependentSlot(i)) {
                hotbar[i] = null;
                logger.fine("Skipping independent slot " + i + " when getting hotbar for player " + player.getName());
                continue;
            }

            ItemStack item = inventory.getItem(i);
            hotbar[i] = item != null ? ItemSerializer.cloneItemStack(item) : null;
        }

        return hotbar;
    }

    /**
     * Sets the player's hotbar items.
     * 
     * @param player The player
     * @param hotbar Array of ItemStacks to set in the hotbar
     */
    private void setPlayerHotbar(Player player, ItemStack[] hotbar) {
        PlayerInventory inventory = player.getInventory();

        for (int i = 0; i < 9 && i < hotbar.length; i++) {
            // Skip independent slots - they won't be modified
            if (configManager.isIndependentSlot(i)) {
                logger.fine("Skipping independent slot " + i + " when setting hotbar for player " + player.getName());
                continue;
            }

            ItemStack item = hotbar[i];
            inventory.setItem(i, item != null ? ItemSerializer.cloneItemStack(item) : null);
        }
    }

    /**
     * Checks if a hotbar is empty (all slots are null).
     * 
     * @param hotbar The hotbar to check
     * @return true if the hotbar is empty, false otherwise
     */
    private boolean isHotbarEmpty(ItemStack[] hotbar) {
        if (hotbar == null) {
            return true;
        }

        for (ItemStack item : hotbar) {
            if (item != null) {
                return false;
            }
        }

        return true;
    }

    /**
     * Checks if an ItemStack is a switcher item.
     * 
     * @param item The ItemStack to check
     * @return true if it's a switcher item, false otherwise
     */
    public boolean isSwitcherItem(ItemStack item) {
        if (item == null) {
            return false;
        }

        ItemStack switcherItem = configManager.getSwitcherItem();
        return ItemSerializer.areItemsSimilar(item, switcherItem);
    }

    /**
     * Finds a slot that is not the switcher slot.
     * 
     * @param switcherSlot The switcher slot to avoid
     * @return A slot number that is not the switcher slot
     */
    private int findNonSwitcherSlot(int switcherSlot) {
        // Try to find the closest slot to the switcher slot
        for (int offset = 1; offset < 9; offset++) {
            int leftSlot = switcherSlot - offset;
            int rightSlot = switcherSlot + offset;

            if (leftSlot >= 0) {
                return leftSlot;
            }
            if (rightSlot < 9) {
                return rightSlot;
            }
        }

        // Fallback to slot 0 if somehow all slots are switcher slots (shouldn't happen)
        return 0;
    }

    /**
     * Handles player logout, saving their current hotbar state.
     * 
     * @param player The player logging out
     */
    public void handlePlayerLogout(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);

        // Save current hotbar state
        ItemStack[] currentHotbar = getPlayerHotbar(player);
        data.updateCurrentHotbar(currentHotbar);

        // Save held item slot
        data.setCurrentHeldItemSlot(player.getInventory().getHeldItemSlot());

        // Save and unload player data
        playerDataManager.unloadPlayerData(player);

        logger.info("Saved hotbar data for player: " + player.getName());
    }

    /**
     * Gets the current hotbar state for a player.
     * 
     * @param player The player
     * @return String describing the current hotbar state
     */
    public String getHotbarState(Player player) {
        PlayerHotbarData data = playerDataManager.getPlayerData(player);
        return data.isUsingSecondaryHotbar() ? "Secondary" : "Primary";
    }
}
