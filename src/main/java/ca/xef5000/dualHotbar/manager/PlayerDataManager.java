package ca.xef5000.dualHotbar.manager;

import ca.xef5000.dualHotbar.DualHotbar;
import ca.xef5000.dualHotbar.data.PlayerHotbarData;
import ca.xef5000.dualHotbar.util.ItemSerializer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Manages player hotbar data including loading, saving, and caching.
 * Provides robust data persistence with multiple failsafe mechanisms.
 */
public class PlayerDataManager {
    
    private final DualHotbar plugin;
    private final Logger logger;
    private final Map<UUID, PlayerHotbarData> playerDataCache;
    private final File dataFolder;
    private BukkitTask autoSaveTask;
    
    /**
     * Creates a new PlayerDataManager instance.
     * 
     * @param plugin The DualHotbar plugin instance
     */
    public PlayerDataManager(DualHotbar plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.playerDataCache = new ConcurrentHashMap<>();
        this.dataFolder = new File(plugin.getDataFolder(), "playerdata");
        
        // Create data folder if it doesn't exist
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        // Start auto-save task if enabled
        startAutoSaveTask();
    }
    
    /**
     * Gets the hotbar data for a player, loading it if not cached.
     * 
     * @param player The player to get data for
     * @return The player's hotbar data
     */
    public PlayerHotbarData getPlayerData(Player player) {
        return getPlayerData(player.getUniqueId());
    }
    
    /**
     * Gets the hotbar data for a player by UUID, loading it if not cached.
     * 
     * @param playerId The UUID of the player
     * @return The player's hotbar data
     */
    public PlayerHotbarData getPlayerData(UUID playerId) {
        PlayerHotbarData data = playerDataCache.get(playerId);
        if (data == null) {
            data = loadPlayerData(playerId);
            playerDataCache.put(playerId, data);
        }
        return data;
    }
    
    /**
     * Loads player data from file.
     *
     * @param playerId The UUID of the player
     * @return The loaded player data, or a new instance if loading fails
     */
    private PlayerHotbarData loadPlayerData(UUID playerId) {
        File playerFile = new File(dataFolder, playerId.toString() + ".yml");
        int totalExtraHotbars = plugin.getConfigManager().getTotalExtraHotbars();
        PlayerHotbarData data = new PlayerHotbarData(playerId, totalExtraHotbars);

        if (!playerFile.exists()) {
            return data;
        }

        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(playerFile);

            // Load all hotbars
            for (int hotbarId = 0; hotbarId <= totalExtraHotbars; hotbarId++) {
                String hotbarPath = "hotbar-" + hotbarId;
                if (config.contains(hotbarPath)) {
                    ItemStack[] hotbar = loadHotbarFromConfig(config, hotbarPath);
                    if (hotbar != null) {
                        data.setHotbar(hotbarId, hotbar);
                    }
                }
            }

            // Load active hotbars
            if (config.contains("active-hotbars")) {
                for (int hotbarId : config.getIntegerList("active-hotbars")) {
                    data.activateHotbar(hotbarId, plugin.getConfigManager().getMaxActiveHotbars());
                }
            }

            // Load other data
            data.setCurrentHotbarId(config.getInt("current-hotbar", 0));
            data.setCurrentHeldItemSlot(config.getInt("held-slot", 0));

            logger.info("Loaded multi-hotbar data for player: " + playerId);

        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to load player data for " + playerId + ", using defaults", e);

            // Try to recover from backup if available
            if (!data.recoverFromBackup()) {
                logger.warning("No backup available for player " + playerId + ", starting with empty hotbars");
            }
        }

        return data;
    }
    
    /**
     * Loads a hotbar from configuration using multiple fallback methods.
     * 
     * @param config The configuration to load from
     * @param path The path to the hotbar data
     * @return The loaded hotbar items, or null if loading fails
     */
    private ItemStack[] loadHotbarFromConfig(FileConfiguration config, String path) {
        ItemStack[] hotbar = new ItemStack[9];
        
        // Try to load using Base64 serialization first (most reliable)
        if (config.contains(path + ".serialized")) {
            String serializedData = config.getString(path + ".serialized");
            ItemStack[] deserializedHotbar = ItemSerializer.deserializeItemStackArray(serializedData);
            if (deserializedHotbar != null && deserializedHotbar.length == 9) {
                return deserializedHotbar;
            }
        }
        
        // Fallback to individual item loading
        ConfigurationSection hotbarSection = config.getConfigurationSection(path + ".items");
        if (hotbarSection != null) {
            for (String slotStr : hotbarSection.getKeys(false)) {
                try {
                    int slot = Integer.parseInt(slotStr);
                    if (slot >= 0 && slot < 9) {
                        ConfigurationSection itemSection = hotbarSection.getConfigurationSection(slotStr);
                        if (itemSection != null) {
                            ItemStack item = ItemSerializer.deserializeItemStackFromConfig(itemSection);
                            if (item != null) {
                                hotbar[slot] = item;
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    logger.warning("Invalid slot number in config: " + slotStr);
                }
            }
            return hotbar;
        }
        
        return null;
    }
    
    /**
     * Saves player data to file.
     * 
     * @param playerId The UUID of the player
     * @param data The player data to save
     */
    public void savePlayerData(UUID playerId, PlayerHotbarData data) {
        File playerFile = new File(dataFolder, playerId.toString() + ".yml");
        
        try {
            FileConfiguration config = new YamlConfiguration();
            
            // Save primary hotbar
            saveHotbarToConfig(config, "primary-hotbar", data.getPrimaryHotbar());
            
            // Save secondary hotbar
            saveHotbarToConfig(config, "secondary-hotbar", data.getSecondaryHotbar());
            
            // Save other data
            config.set("using-secondary", data.isUsingSecondaryHotbar());
            config.set("held-slot", data.getCurrentHeldItemSlot());
            config.set("last-save", System.currentTimeMillis());
            
            config.save(playerFile);
            
            // Create backup after successful save
            data.createBackup();
            
        } catch (IOException e) {
            logger.log(Level.SEVERE, "Failed to save player data for " + playerId, e);
        }
    }
    
    /**
     * Saves a hotbar to configuration using multiple methods for redundancy.
     * 
     * @param config The configuration to save to
     * @param path The path to save the hotbar data
     * @param hotbar The hotbar items to save
     */
    private void saveHotbarToConfig(FileConfiguration config, String path, ItemStack[] hotbar) {
        if (hotbar == null || hotbar.length != 9) {
            return;
        }
        
        // Save using Base64 serialization (primary method)
        String serializedData = ItemSerializer.serializeItemStackArray(hotbar);
        if (serializedData != null) {
            config.set(path + ".serialized", serializedData);
        }
        
        // Save individual items as fallback
        for (int i = 0; i < hotbar.length; i++) {
            ItemStack item = hotbar[i];
            if (item != null) {
                ConfigurationSection itemSection = ItemSerializer.serializeItemStackToConfig(item);
                if (itemSection != null) {
                    config.set(path + ".items." + i, itemSection);
                }
            }
        }
    }
    
    /**
     * Saves data for a specific player.
     * 
     * @param player The player to save data for
     */
    public void savePlayerData(Player player) {
        PlayerHotbarData data = playerDataCache.get(player.getUniqueId());
        if (data != null) {
            savePlayerData(player.getUniqueId(), data);
        }
    }
    
    /**
     * Saves all cached player data.
     */
    public void saveAllPlayerData() {
        for (Map.Entry<UUID, PlayerHotbarData> entry : playerDataCache.entrySet()) {
            savePlayerData(entry.getKey(), entry.getValue());
        }
        logger.info("Saved data for " + playerDataCache.size() + " players");
    }
    
    /**
     * Removes a player from the cache and saves their data.
     * 
     * @param playerId The UUID of the player to unload
     */
    public void unloadPlayerData(UUID playerId) {
        PlayerHotbarData data = playerDataCache.remove(playerId);
        if (data != null) {
            savePlayerData(playerId, data);
        }
    }
    
    /**
     * Removes a player from the cache and saves their data.
     * 
     * @param player The player to unload
     */
    public void unloadPlayerData(Player player) {
        unloadPlayerData(player.getUniqueId());
    }
    
    /**
     * Checks if a player has data cached.
     * 
     * @param playerId The UUID of the player
     * @return true if the player has cached data, false otherwise
     */
    public boolean hasPlayerData(UUID playerId) {
        return playerDataCache.containsKey(playerId);
    }
    
    /**
     * Gets the number of players with cached data.
     * 
     * @return The number of cached players
     */
    public int getCachedPlayerCount() {
        return playerDataCache.size();
    }
    
    /**
     * Starts the auto-save task if enabled in configuration.
     */
    private void startAutoSaveTask() {
        int autoSaveInterval = plugin.getConfigManager().getAutoSaveInterval();

        if (autoSaveInterval > 0) {
            // Convert minutes to ticks (20 ticks per second, 60 seconds per minute)
            long intervalTicks = autoSaveInterval * 20L * 60L;

            autoSaveTask = new BukkitRunnable() {
                @Override
                public void run() {
                    saveAllPlayerData();
                }
            }.runTaskTimerAsynchronously(plugin, intervalTicks, intervalTicks);

            logger.info("Auto-save task started with interval: " + autoSaveInterval + " minutes");
        }
    }

    /**
     * Stops the auto-save task.
     */
    private void stopAutoSaveTask() {
        if (autoSaveTask != null && !autoSaveTask.isCancelled()) {
            autoSaveTask.cancel();
            autoSaveTask = null;
            logger.info("Auto-save task stopped");
        }
    }

    /**
     * Clears all cached data and saves it to disk.
     */
    public void shutdown() {
        logger.info("Shutting down PlayerDataManager, saving all data...");

        // Stop auto-save task
        stopAutoSaveTask();

        // Save all data
        saveAllPlayerData();
        playerDataCache.clear();
    }
}
