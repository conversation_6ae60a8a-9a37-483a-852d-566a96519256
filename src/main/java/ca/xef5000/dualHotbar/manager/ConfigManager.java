package ca.xef5000.dualHotbar.manager;

import ca.xef5000.dualHotbar.DualHotbar;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.ChatColor;
import dev.lone.itemsadder.api.CustomStack;

import java.util.List;

public class ConfigManager {

    private final DualHotbar plugin;

    private int switcherSlot;
    private String switcherItemMaterial;
    private String switcherItemName;
    private String[] switcherItemLore;

    public ConfigManager(DualHotbar dualHotbar) {
        this.plugin = dualHotbar;
        loadConfig();
    }

    public void loadConfig() {
        FileConfiguration config = plugin.getConfig();
        this.switcherSlot = config.getInt("switcher-slot", 8);
        this.switcherItemMaterial = config.getString("switcher-item.material", "NETHER_STAR");
        this.switcherItemName = config.getString("switcher-item.name", "&cSwitch to Second Hotbar");
        List<String> loreList = config.getStringList("switcher-item.lore");
        this.switcherItemLore = loreList.toArray(new String[0]);
    }

    public int getSwitcherSlot() {
        return switcherSlot;
    }

    public ItemStack getSwitcherItem() {
        ItemStack item = null;
        if (switcherItemMaterial != null && (switcherItemMaterial.startsWith("itemsadder:") || switcherItemMaterial.startsWith("ia:"))) {
            // ItemsAdder custom item
            String iaId = switcherItemMaterial.replaceFirst("^(itemsadder:|ia:)", "");
            CustomStack customStack = CustomStack.getInstance(iaId);
            if (customStack != null) {
                item = customStack.getItemStack().clone();
            }
        }
        if (item == null) {
            Material material;
            try {
                material = Material.matchMaterial(switcherItemMaterial);
            } catch (Exception e) {
                material = Material.NETHER_STAR;
            }
            if (material == null) {
                material = Material.NETHER_STAR;
            }
            item = new ItemStack(material);
        }
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', switcherItemName));
            if (switcherItemLore != null && switcherItemLore.length > 0) {
                List<String> lore = new java.util.ArrayList<>();
                for (String line : switcherItemLore) {
                    lore.add(ChatColor.translateAlternateColorCodes('&', line));
                }
                meta.setLore(lore);
            }
            item.setItemMeta(meta);
        }
        return item;
    }
}
