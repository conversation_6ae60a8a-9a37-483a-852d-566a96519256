package ca.xef5000.dualHotbar.manager;

import ca.xef5000.dualHotbar.DualHotbar;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.ChatColor;
import dev.lone.itemsadder.api.CustomStack;

import java.util.List;

public class ConfigManager {

    private final DualHotbar plugin;

    private int switcherSlot;
    private String switcherItemMaterial;
    private String switcherItemName;
    private String[] switcherItemLore;

    // Multi-hotbar settings
    private int totalExtraHotbars;
    private int maxActiveHotbars;
    private String guiCommand;
    private String guiTitle;
    private int guiSize;

    // Control slot items
    private ItemStack activeControlItem;
    private ItemStack inactiveControlItem;
    private ItemStack equippedControlItem;
    private ItemStack placeholderItem;

    // Data storage settings
    private int autoSaveInterval;
    private boolean createBackups;
    private int maxBackups;

    // Performance settings
    private int playerInitDelay;
    private int hotbarUpdateDelay;

    // Independent slots settings
    private int[] independentSlots;

    public ConfigManager(DualHotbar dualHotbar) {
        this.plugin = dualHotbar;
        loadConfig();
    }

    public void loadConfig() {
        FileConfiguration config = plugin.getConfig();

        // Switcher settings
        this.switcherSlot = config.getInt("switcher-slot", 8);
        this.switcherItemMaterial = config.getString("switcher-item.material", "NETHER_STAR");
        this.switcherItemName = config.getString("switcher-item.name", "&cSwitch to Second Hotbar");
        List<String> loreList = config.getStringList("switcher-item.lore");
        this.switcherItemLore = loreList.toArray(new String[0]);

        // Data storage settings
        this.autoSaveInterval = config.getInt("data-storage.auto-save-interval", 5);
        this.createBackups = config.getBoolean("data-storage.create-backups", true);
        this.maxBackups = config.getInt("data-storage.max-backups", 3);

        // Performance settings
        this.playerInitDelay = config.getInt("performance.player-init-delay", 5);
        this.hotbarUpdateDelay = config.getInt("performance.hotbar-update-delay", 1);

        // Independent slots settings
        List<Integer> independentSlotsList = config.getIntegerList("independent-slots");
        this.independentSlots = new int[independentSlotsList.size()];
        for (int i = 0; i < independentSlotsList.size(); i++) {
            this.independentSlots[i] = independentSlotsList.get(i);
        }
    }

    public int getSwitcherSlot() {
        return switcherSlot;
    }

    public ItemStack getSwitcherItem() {
        ItemStack item = null;
        if (switcherItemMaterial != null && (switcherItemMaterial.startsWith("itemsadder:") || switcherItemMaterial.startsWith("ia:"))) {
            // ItemsAdder custom item
            String iaId = switcherItemMaterial.replaceFirst("^(itemsadder:|ia:)", "");
            CustomStack customStack = CustomStack.getInstance(iaId);
            if (customStack != null) {
                item = customStack.getItemStack().clone();
            }
        }
        if (item == null) {
            Material material;
            try {
                material = Material.matchMaterial(switcherItemMaterial);
            } catch (Exception e) {
                material = Material.NETHER_STAR;
            }
            if (material == null) {
                material = Material.NETHER_STAR;
            }
            item = new ItemStack(material);
        }
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', switcherItemName));
            if (switcherItemLore != null && switcherItemLore.length > 0) {
                List<String> lore = new java.util.ArrayList<>();
                for (String line : switcherItemLore) {
                    lore.add(ChatColor.translateAlternateColorCodes('&', line));
                }
                meta.setLore(lore);
            }
            item.setItemMeta(meta);
        }
        return item;
    }

    public int getAutoSaveInterval() {
        return autoSaveInterval;
    }

    public boolean shouldCreateBackups() {
        return createBackups;
    }

    public int getMaxBackups() {
        return maxBackups;
    }

    public int getPlayerInitDelay() {
        return playerInitDelay;
    }

    public int getHotbarUpdateDelay() {
        return hotbarUpdateDelay;
    }

    /**
     * Gets the array of independent slots that should not be affected by hotbar swaps.
     * 
     * @return Array of slot numbers (0-8) that should remain independent
     */
    public int[] getIndependentSlots() {
        return independentSlots;
    }

    /**
     * Checks if a slot is in the list of independent slots.
     * 
     * @param slot The slot number to check
     * @return true if the slot is independent, false otherwise
     */
    public boolean isIndependentSlot(int slot) {
        for (int independentSlot : independentSlots) {
            if (independentSlot == slot) {
                return true;
            }
        }
        return false;
    }
}
