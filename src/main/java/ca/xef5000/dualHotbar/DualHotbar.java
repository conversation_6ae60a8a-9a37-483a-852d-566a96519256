package ca.xef5000.dualHotbar;

import ca.xef5000.dualHotbar.manager.ConfigManager;
import org.bukkit.plugin.java.JavaPlugin;

public final class DualHotbar extends JavaPlugin {

    private ConfigManager configManager;

    @Override
    public void onEnable() {
        // Plugin startup logic
        saveDefaultConfig();
        configManager = new ConfigManager(this);
    }

    @Override
    public void onDisable() {
        // Plugin shutdown logic
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }
}
