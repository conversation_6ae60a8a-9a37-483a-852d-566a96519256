package ca.xef5000.dualHotbar;

import ca.xef5000.dualHotbar.command.DualHotbarCommand;
import ca.xef5000.dualHotbar.command.HotbarsCommand;
import ca.xef5000.dualHotbar.gui.HotbarGUI;
import ca.xef5000.dualHotbar.listener.HotbarGUIListener;
import ca.xef5000.dualHotbar.listener.PlayerInteractionListener;
import ca.xef5000.dualHotbar.manager.ConfigManager;
import ca.xef5000.dualHotbar.manager.HotbarManager;
import ca.xef5000.dualHotbar.manager.PlayerDataManager;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

public final class DualHotbar extends JavaPlugin {

    private ConfigManager configManager;
    private PlayerDataManager playerDataManager;
    private HotbarManager hotbarManager;
    private PlayerInteractionListener playerListener;

    @Override
    public void onEnable() {
        // Plugin startup logic
        getLogger().info("Enabling DualHotbar plugin...");

        // Save default configuration
        saveDefaultConfig();

        // Initialize managers in correct order
        configManager = new ConfigManager(this);
        playerDataManager = new PlayerDataManager(this);
        hotbarManager = new HotbarManager(this, playerDataManager, configManager);

        // Register event listeners
        playerListener = new PlayerInteractionListener(this, hotbarManager, playerDataManager, configManager);
        getServer().getPluginManager().registerEvents(playerListener, this);

        // Register commands
        DualHotbarCommand commandHandler = new DualHotbarCommand(this);
        getCommand("dualhotbar").setExecutor(commandHandler);
        getCommand("dualhotbar").setTabCompleter(commandHandler);

        // Initialize existing online players (in case of reload)
        new BukkitRunnable() {
            @Override
            public void run() {
                getServer().getOnlinePlayers().forEach(player -> {
                    hotbarManager.initializePlayer(player);
                });
            }
        }.runTaskLater(this, 20L); // 1 second delay

        getLogger().info("DualHotbar plugin enabled successfully!");
    }

    @Override
    public void onDisable() {
        // Plugin shutdown logic
        getLogger().info("Disabling DualHotbar plugin...");

        // Save all player data before shutdown
        if (playerDataManager != null) {
            playerDataManager.shutdown();
        }

        getLogger().info("DualHotbar plugin disabled successfully!");
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }

    public HotbarManager getHotbarManager() {
        return hotbarManager;
    }
}
