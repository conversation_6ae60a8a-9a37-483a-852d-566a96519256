# The slot (in the hotbar) that will switch the hotbar to the second hotbar
# Range: 0-8
switcher-slot: 8

switcher-item:
  # See https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/Material.html for a list of material
  # Also supports items from itemsadder and nexo
  # For itemsadder, use the name of the item (e.g. itemsadder:namespace:my_item)
  # For nexo, use the name of the item (e.g. nexo:my_item)
  material: NETHER_STAR
  name: '&cSwitch to Second Hotbar'
  lore:
    - '&7Switch to the switcher slot to change hotbars'
    - '&7You cannot hold or interact with this item'

# Data storage settings
data-storage:
  # How often to auto-save player data (in minutes)
  # Set to 0 to disable auto-save
  auto-save-interval: 5

  # Whether to create backup files for player data
  create-backups: true

  # Maximum number of backup files to keep per player
  max-backups: 3

# Performance settings
performance:
  # Delay in ticks before initializing players on join (prevents issues with other plugins)
  player-init-delay: 5

  # Delay in ticks for hotbar updates (prevents rapid switching issues)
  hotbar-update-delay: 1

# Slots that will remain independent and not be affected by hotbar swaps
# These slots will not be saved or swapped between hotbars
# Range: 0-8 (slot numbers in the hotbar)
independent-slots: [8]
