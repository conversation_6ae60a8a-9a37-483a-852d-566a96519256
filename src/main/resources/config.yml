# The slot (in the hotbar) that will cycle through active hotbars
# Range: 0-8
switcher-slot: 8

switcher-item:
  # See https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/Material.html for a list of material
  # Also supports items from itemsadder and nexo
  # For itemsadder, use the name of the item (e.g. itemsadder:namespace:my_item)
  # For nexo, use the name of the item (e.g. nexo:my_item)
  material: NETHER_STAR
  name: '&cCycle Hotbars'
  lore:
    - '&7Switch to this slot to cycle through active hotbars'
    - '&7You cannot hold or interact with this item'

# Multi-hotbar system settings
hotbars:
  # Total number of extra hotbars (excluding the normal hotbar)
  total-extra-hotbars: 6

  # Maximum number of hotbars that can be active at once
  # Active hotbars can be cycled through with the switcher item
  max-active-hotbars: 2

  # Command to open the hotbar management GUI
  gui-command: "hotbars"

  # GUI settings
  gui:
    title: '&8Hotbar Management'
    size: 54  # Must be multiple of 9 (9, 18, 27, 36, 45, 54)

    # Control slot settings (for activating/deactivating hotbars)
    control-slot:
      # Item when hotbar is active
      active-item:
        material: LIME_DYE
        name: '&aActive Hotbar'
        lore:
          - '&7This hotbar is currently active'
          - '&7Click to deactivate'

      # Item when hotbar is inactive
      inactive-item:
        material: RED_DYE
        name: '&cInactive Hotbar'
        lore:
          - '&7This hotbar is currently inactive'
          - '&7Click to activate'

      # Item when hotbar is currently equipped
      equipped-item:
        material: GOLD_INGOT
        name: '&6Currently Equipped'
        lore:
          - '&7This hotbar is currently equipped'
          - '&7Cannot be deactivated while equipped'

    # Placeholder item for independent slots
    placeholder-item:
      material: GRAY_STAINED_GLASS_PANE
      name: '&7Independent Slot'
      lore:
        - '&7This slot is controlled by the switcher item'

# Data storage settings
data-storage:
  # How often to auto-save player data (in minutes)
  # Set to 0 to disable auto-save
  auto-save-interval: 5

  # Whether to create backup files for player data
  create-backups: true

  # Maximum number of backup files to keep per player
  max-backups: 3

# Performance settings
performance:
  # Delay in ticks before initializing players on join (prevents issues with other plugins)
  player-init-delay: 5

  # Delay in ticks for hotbar updates (prevents rapid switching issues)
  hotbar-update-delay: 1

# Slots that will remain independent and not be affected by hotbar swaps
# These slots will not be saved or swapped between hotbars
# Range: 0-8 (slot numbers in the hotbar)
independent-slots: [8]
